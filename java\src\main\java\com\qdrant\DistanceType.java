package com.qdrant;

/**
 * Enumeration of distance metrics supported by Qdrant.
 */
public enum DistanceType {
    /**
     * Cosine distance - measures the cosine of the angle between vectors.
     * Good for high-dimensional data and when vector magnitude is not important.
     */
    COSINE(0),
    
    /**
     * Euclidean distance - measures the straight-line distance between vectors.
     * Good for low-dimensional data and when vector magnitude matters.
     */
    EUCLIDEAN(1),
    
    /**
     * Dot product - measures the dot product between vectors.
     * Good for normalized vectors and when you want to maximize similarity.
     */
    DOT(2);
    
    private final int value;
    
    DistanceType(int value) {
        this.value = value;
    }
    
    /**
     * Get the integer value for JNI usage.
     * 
     * @return Integer value
     */
    public int getValue() {
        return value;
    }
    
    /**
     * Get DistanceType from integer value.
     * 
     * @param value Integer value
     * @return DistanceType
     * @throws IllegalArgumentException if value is invalid
     */
    public static DistanceType fromValue(int value) {
        for (DistanceType type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid distance type value: " + value);
    }
}
