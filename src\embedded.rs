use crate::error::{QdrantError, Result};
use crate::types::*;
use qdrant_client::prelude::*;
use qdrant_client::qdrant::{
    vectors_config::Config, CreateCollection, Distance as QdrantDistance, HnswConfigDiff,
    OptimizersConfigDiff, PointStruct, SearchPoints, VectorParams, VectorsConfig,
};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct QdrantEmbedded {
    client: Arc<Mutex<QdrantClient>>,
    storage_path: PathBuf,
}

impl QdrantEmbedded {
    /// Create a new embedded Qdrant instance
    pub async fn new(storage_path: impl Into<PathBuf>) -> Result<Self> {
        let storage_path = storage_path.into();
        
        // Ensure storage directory exists
        if let Some(parent) = storage_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        
        // Create embedded Qdrant client
        let client = QdrantClient::from_url(&format!("file://{}", storage_path.display()))
            .build()
            .map_err(QdrantError::Client)?;
        
        Ok(Self {
            client: Arc::new(Mutex::new(client)),
            storage_path,
        })
    }

    /// Create a new vector collection
    pub async fn create_collection(&self, config: CollectionConfig) -> Result<()> {
        let client = self.client.lock().await;
        
        let distance = match config.distance {
            Distance::Cosine => QdrantDistance::Cosine,
            Distance::Euclidean => QdrantDistance::Euclid,
            Distance::Dot => QdrantDistance::Dot,
        };

        let hnsw_config = config.hnsw_config.unwrap_or_default();
        let optimizers_config = config.optimizers_config.unwrap_or_default();

        let vectors_config = VectorsConfig {
            config: Some(Config::Params(VectorParams {
                size: config.vector_size as u64,
                distance: distance.into(),
                hnsw_config: Some(HnswConfigDiff {
                    m: hnsw_config.m.map(|v| v as u64),
                    ef_construct: hnsw_config.ef_construct.map(|v| v as u64),
                    full_scan_threshold: hnsw_config.full_scan_threshold.map(|v| v as u64),
                    max_indexing_threads: hnsw_config.max_indexing_threads.map(|v| v as u64),
                    ..Default::default()
                }),
                quantization_config: None,
                on_disk: None,
            })),
        };

        let optimizers_config_diff = OptimizersConfigDiff {
            deleted_threshold: optimizers_config.deleted_threshold,
            vacuum_min_vector_number: optimizers_config.vacuum_min_vector_number.map(|v| v as u64),
            default_segment_number: optimizers_config.default_segment_number.map(|v| v as u64),
            max_segment_size: optimizers_config.max_segment_size.map(|v| v as u64),
            memmap_threshold: optimizers_config.memmap_threshold.map(|v| v as u64),
            indexing_threshold: optimizers_config.indexing_threshold.map(|v| v as u64),
            flush_interval_sec: optimizers_config.flush_interval_sec,
            max_optimization_threads: optimizers_config.max_optimization_threads.map(|v| v as u64),
        };

        client
            .create_collection(&CreateCollection {
                collection_name: config.name,
                vectors_config: Some(vectors_config),
                shard_number: Some(1),
                replication_factor: Some(1),
                write_consistency_factor: Some(1),
                on_disk_payload: Some(true),
                hnsw_config: Some(hnsw_config.into()),
                optimizers_config: Some(optimizers_config_diff),
                wal_config: None,
                quantization_config: None,
                init_from: None,
                timeout: None,
            })
            .await
            .map_err(QdrantError::Client)?;

        Ok(())
    }

    /// Insert vectors into a collection
    pub async fn insert_vectors(
        &self,
        collection_name: &str,
        points: Vec<VectorPoint>,
    ) -> Result<()> {
        let client = self.client.lock().await;

        let qdrant_points: Vec<PointStruct> = points
            .into_iter()
            .map(|point| {
                let payload = point.payload.unwrap_or_default();
                PointStruct::new(
                    point.id,
                    point.vector,
                    payload,
                )
            })
            .collect();

        client
            .upsert_points_blocking(collection_name, None, qdrant_points, None)
            .await
            .map_err(QdrantError::Client)?;

        Ok(())
    }

    /// Update existing vectors in a collection
    pub async fn update_vectors(
        &self,
        collection_name: &str,
        points: Vec<VectorPoint>,
    ) -> Result<()> {
        // For Qdrant, update is the same as upsert
        self.insert_vectors(collection_name, points).await
    }

    /// Delete vectors by IDs
    pub async fn delete_vectors(
        &self,
        collection_name: &str,
        point_ids: Vec<String>,
    ) -> Result<()> {
        let client = self.client.lock().await;

        let point_ids: Vec<PointId> = point_ids
            .into_iter()
            .map(PointId::from)
            .collect();

        client
            .delete_points(collection_name, None, &point_ids, None)
            .await
            .map_err(QdrantError::Client)?;

        Ok(())
    }

    /// Search for similar vectors
    pub async fn search_vectors(
        &self,
        collection_name: &str,
        query_vector: Vec<f32>,
        params: SearchParams,
    ) -> Result<Vec<SearchResult>> {
        let client = self.client.lock().await;

        let search_points = SearchPoints {
            collection_name: collection_name.to_string(),
            vector: query_vector,
            filter: params.filter.map(|f| serde_json::from_value(f).unwrap_or_default()),
            limit: params.limit as u64,
            offset: params.offset.map(|o| o as u64),
            with_payload: Some(params.with_payload.into()),
            with_vectors: Some(params.with_vector.into()),
            score_threshold: params.score_threshold,
            params: None,
            read_consistency: None,
            timeout: None,
        };

        let search_result = client
            .search_points(&search_points)
            .await
            .map_err(QdrantError::Client)?;

        let results = search_result
            .result
            .into_iter()
            .map(|scored_point| {
                let payload = if params.with_payload {
                    Some(scored_point.payload)
                } else {
                    None
                };

                let vector = if params.with_vector {
                    scored_point.vectors.and_then(|v| v.vector)
                        .and_then(|v| match v {
                            qdrant_client::qdrant::vectors::Vector::Dense(dense) => Some(dense.data),
                            _ => None,
                        })
                } else {
                    None
                };

                SearchResult {
                    id: scored_point.id.unwrap().point_id_options.unwrap().to_string(),
                    score: scored_point.score,
                    payload,
                    vector,
                }
            })
            .collect();

        Ok(results)
    }

    /// Get specific vector by ID
    pub async fn get_vector(
        &self,
        collection_name: &str,
        point_id: &str,
        with_payload: bool,
        with_vector: bool,
    ) -> Result<Option<VectorPoint>> {
        let client = self.client.lock().await;

        let points = client
            .get_points(
                collection_name,
                None,
                &[PointId::from(point_id.to_string())],
                Some(with_payload),
                Some(with_vector),
                None,
            )
            .await
            .map_err(QdrantError::Client)?;

        if let Some(point) = points.result.into_iter().next() {
            let vector = if with_vector {
                point.vectors.and_then(|v| v.vector)
                    .and_then(|v| match v {
                        qdrant_client::qdrant::vectors::Vector::Dense(dense) => Some(dense.data),
                        _ => None,
                    })
                    .unwrap_or_default()
            } else {
                Vec::new()
            };

            let payload = if with_payload {
                Some(point.payload)
            } else {
                None
            };

            Ok(Some(VectorPoint {
                id: point.id.unwrap().point_id_options.unwrap().to_string(),
                vector,
                payload,
            }))
        } else {
            Ok(None)
        }
    }

    /// List all collections
    pub async fn list_collections(&self) -> Result<Vec<String>> {
        let client = self.client.lock().await;

        let collections = client
            .list_collections()
            .await
            .map_err(QdrantError::Client)?;

        Ok(collections.collections.into_iter().map(|c| c.name).collect())
    }

    /// Delete a collection
    pub async fn delete_collection(&self, collection_name: &str) -> Result<()> {
        let client = self.client.lock().await;

        client
            .delete_collection(collection_name)
            .await
            .map_err(QdrantError::Client)?;

        Ok(())
    }

    /// Get collection info
    pub async fn collection_info(&self, collection_name: &str) -> Result<serde_json::Value> {
        let client = self.client.lock().await;

        let info = client
            .collection_info(collection_name)
            .await
            .map_err(QdrantError::Client)?;

        serde_json::to_value(info).map_err(QdrantError::Serialization)
    }
}
