@echo off
setlocal enabledelayedexpansion

REM Cross-platform build script for Qdrant JNI (Windows)

echo [INFO] Starting Qdrant JNI build for Windows...

REM Check if JAVA_HOME is set
if "%JAVA_HOME%"=="" (
    echo [ERROR] JAVA_HOME environment variable is not set
    echo [ERROR] Please set JAVA_HOME to your Java installation directory
    exit /b 1
)

echo [INFO] Using JAVA_HOME: %JAVA_HOME%

REM Check if Rust is installed
where cargo >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Rust/Cargo is not installed
    echo [ERROR] Please install Rust from https://rustup.rs/
    exit /b 1
)

REM Check if Maven is installed
where mvn >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] Maven is not installed. Java build will be skipped.
    set SKIP_JAVA=true
) else (
    set SKIP_JAVA=false
)

REM Create necessary directories
if not exist "target\release" mkdir "target\release"
if not exist "include" mkdir "include"

REM Build the Rust library
echo [INFO] Building Rust library...
cargo build --release

if %errorlevel% neq 0 (
    echo [ERROR] Rust build failed
    exit /b 1
)

echo [INFO] Rust library built successfully

REM Copy the built library
if exist "target\release\qdrant_jni.dll" (
    copy "target\release\qdrant_jni.dll" "target\release\" >nul
    echo [INFO] Windows DLL ready: target\release\qdrant_jni.dll
)

REM Build Java components if Maven is available
if "%SKIP_JAVA%"=="false" (
    echo [INFO] Building Java components...
    cd java
    
    REM Compile Java classes
    call mvn clean compile
    
    if !errorlevel! neq 0 (
        echo [ERROR] Java compilation failed
        exit /b 1
    )
    
    REM Generate JNI headers
    call mvn exec:exec@generate-jni-headers
    
    REM Package JAR
    call mvn package -DskipTests
    
    if !errorlevel! neq 0 (
        echo [ERROR] Java packaging failed
        exit /b 1
    )
    
    cd ..
    echo [INFO] Java components built successfully
) else (
    echo [WARNING] Skipping Java build (Maven not available)
)

REM Create test verification script
echo [INFO] Creating test verification...

echo @echo off > test_build.bat
echo echo Testing Qdrant JNI build... >> test_build.bat
echo. >> test_build.bat
echo REM Check if the native library exists >> test_build.bat
echo if exist "target\release\qdrant_jni.dll" ( >> test_build.bat
echo     echo ✓ Native library found: target\release\qdrant_jni.dll >> test_build.bat
echo ^) else ( >> test_build.bat
echo     echo ✗ Native library not found: target\release\qdrant_jni.dll >> test_build.bat
echo     exit /b 1 >> test_build.bat
echo ^) >> test_build.bat
echo. >> test_build.bat
echo REM Check if Java JAR exists >> test_build.bat
echo if exist "java\target\qdrant-jni-0.1.0.jar" ( >> test_build.bat
echo     echo ✓ Java JAR found: java\target\qdrant-jni-0.1.0.jar >> test_build.bat
echo ^) else ( >> test_build.bat
echo     echo ✗ Java JAR not found >> test_build.bat
echo ^) >> test_build.bat
echo. >> test_build.bat
echo echo Build verification completed successfully! >> test_build.bat

echo [INFO] Build completed successfully!
echo [INFO] Run 'test_build.bat' to verify the build
echo.
echo [INFO] To use the library:
echo [INFO] 1. Add the native library to your Java library path
echo [INFO] 2. Include the JAR file in your Java classpath
echo [INFO] 3. See examples/ directory for usage examples

endlocal
