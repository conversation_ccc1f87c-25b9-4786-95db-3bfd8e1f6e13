use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VectorPoint {
    pub id: String,
    pub vector: Vec<f32>,
    pub payload: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SearchResult {
    pub id: String,
    pub score: f32,
    pub payload: Option<HashMap<String, serde_json::Value>>,
    pub vector: Option<Vec<f32>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchParams {
    pub limit: usize,
    pub offset: Option<usize>,
    pub with_payload: bool,
    pub with_vector: bool,
    pub score_threshold: Option<f32>,
    pub filter: Option<serde_json::Value>,
}

impl Default for SearchParams {
    fn default() -> Self {
        Self {
            limit: 10,
            offset: None,
            with_payload: true,
            with_vector: false,
            score_threshold: None,
            filter: None,
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CollectionConfig {
    pub name: String,
    pub vector_size: usize,
    pub distance: Distance,
    pub hnsw_config: Option<HnswConfig>,
    pub optimizers_config: Option<OptimizersConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Distance {
    Cosine,
    Euclidean,
    Dot,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HnswConfig {
    pub m: Option<usize>,
    pub ef_construct: Option<usize>,
    pub full_scan_threshold: Option<usize>,
    pub max_indexing_threads: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizersConfig {
    pub deleted_threshold: Option<f64>,
    pub vacuum_min_vector_number: Option<usize>,
    pub default_segment_number: Option<usize>,
    pub max_segment_size: Option<usize>,
    pub memmap_threshold: Option<usize>,
    pub indexing_threshold: Option<usize>,
    pub flush_interval_sec: Option<u64>,
    pub max_optimization_threads: Option<usize>,
}

impl Default for HnswConfig {
    fn default() -> Self {
        Self {
            m: Some(16),
            ef_construct: Some(100),
            full_scan_threshold: Some(10000),
            max_indexing_threads: Some(0),
        }
    }
}

impl Default for OptimizersConfig {
    fn default() -> Self {
        Self {
            deleted_threshold: Some(0.2),
            vacuum_min_vector_number: Some(1000),
            default_segment_number: Some(0),
            max_segment_size: Some(200000),
            memmap_threshold: Some(1000000),
            indexing_threshold: Some(20000),
            flush_interval_sec: Some(5),
            max_optimization_threads: Some(0),
        }
    }
}

// Utility functions for ID generation
pub fn generate_id() -> String {
    Uuid::new_v4().to_string()
}
