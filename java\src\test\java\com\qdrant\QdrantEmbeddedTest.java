package com.qdrant;

import org.junit.jupiter.api.*;
import java.util.*;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for QdrantEmbedded functionality.
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class QdrantEmbeddedTest {
    
    private static final String TEST_STORAGE_PATH = "./test_qdrant_storage";
    private static final String TEST_COLLECTION = "test_collection";
    private static final int VECTOR_SIZE = 4;
    
    private QdrantEmbedded qdrant;
    
    @BeforeEach
    void setUp() throws QdrantException {
        // Clean up any existing test storage
        cleanupTestStorage();
        
        // Create new instance
        qdrant = new QdrantEmbedded(TEST_STORAGE_PATH);
    }
    
    @AfterEach
    void tearDown() {
        if (qdrant != null) {
            qdrant.close();
        }
        cleanupTestStorage();
    }
    
    @Test
    @Order(1)
    @DisplayName("Test collection creation")
    void testCreateCollection() throws QdrantException {
        boolean result = qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        assertTrue(result, "Collection creation should succeed");
    }
    
    @Test
    @Order(2)
    @DisplayName("Test vector insertion")
    void testInsertVectors() throws QdrantException {
        // Create collection first
        qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        
        // Prepare test vectors
        List<VectorPoint> points = createTestVectors();
        
        // Insert vectors
        boolean result = qdrant.insertVectors(TEST_COLLECTION, points);
        assertTrue(result, "Vector insertion should succeed");
    }
    
    @Test
    @Order(3)
    @DisplayName("Test vector search")
    void testSearchVectors() throws QdrantException {
        // Setup collection and vectors
        qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        qdrant.insertVectors(TEST_COLLECTION, createTestVectors());
        
        // Search for similar vectors
        Float[] queryVector = {1.0f, 0.0f, 0.0f, 0.0f};
        List<SearchResult> results = qdrant.searchVectors(
            TEST_COLLECTION, queryVector, 5, true, false, 0.0f
        );
        
        assertNotNull(results, "Search results should not be null");
        assertFalse(results.isEmpty(), "Search should return results");
        
        // Check that results are ordered by score (descending)
        for (int i = 1; i < results.size(); i++) {
            assertTrue(results.get(i-1).getScore() >= results.get(i).getScore(),
                "Results should be ordered by score");
        }
    }
    
    @Test
    @Order(4)
    @DisplayName("Test vector retrieval")
    void testGetVector() throws QdrantException {
        // Setup collection and vectors
        qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        List<VectorPoint> testVectors = createTestVectors();
        qdrant.insertVectors(TEST_COLLECTION, testVectors);
        
        // Retrieve a specific vector
        String testId = testVectors.get(0).getId();
        VectorPoint retrieved = qdrant.getVector(TEST_COLLECTION, testId, true, true);
        
        assertNotNull(retrieved, "Retrieved vector should not be null");
        assertEquals(testId, retrieved.getId(), "Retrieved vector should have correct ID");
        assertNotNull(retrieved.getVector(), "Retrieved vector should have vector data");
        assertNotNull(retrieved.getPayload(), "Retrieved vector should have payload");
        assertEquals(VECTOR_SIZE, retrieved.getVector().length, "Vector should have correct size");
    }
    
    @Test
    @Order(5)
    @DisplayName("Test vector deletion")
    void testDeleteVectors() throws QdrantException {
        // Setup collection and vectors
        qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        List<VectorPoint> testVectors = createTestVectors();
        qdrant.insertVectors(TEST_COLLECTION, testVectors);
        
        // Delete some vectors
        List<String> idsToDelete = Arrays.asList(
            testVectors.get(0).getId(),
            testVectors.get(1).getId()
        );
        
        boolean result = qdrant.deleteVectors(TEST_COLLECTION, idsToDelete);
        assertTrue(result, "Vector deletion should succeed");
        
        // Verify deletion
        for (String id : idsToDelete) {
            VectorPoint retrieved = qdrant.getVector(TEST_COLLECTION, id, true, true);
            assertNull(retrieved, "Deleted vector should not be retrievable");
        }
        
        // Verify remaining vectors still exist
        VectorPoint remaining = qdrant.getVector(TEST_COLLECTION, testVectors.get(2).getId(), true, true);
        assertNotNull(remaining, "Non-deleted vector should still exist");
    }
    
    @Test
    @Order(6)
    @DisplayName("Test different distance types")
    void testDistanceTypes() throws QdrantException {
        // Test each distance type
        for (DistanceType distanceType : DistanceType.values()) {
            String collectionName = "test_" + distanceType.name().toLowerCase();
            
            boolean result = qdrant.createCollection(collectionName, VECTOR_SIZE, distanceType);
            assertTrue(result, "Collection creation should succeed for " + distanceType);
            
            // Insert and search with this distance type
            qdrant.insertVectors(collectionName, createTestVectors());
            
            Float[] queryVector = {1.0f, 0.0f, 0.0f, 0.0f};
            List<SearchResult> results = qdrant.searchVectors(
                collectionName, queryVector, 3, false, false, 0.0f
            );
            
            assertNotNull(results, "Search should work with " + distanceType);
        }
    }
    
    @Test
    @Order(7)
    @DisplayName("Test search with score threshold")
    void testSearchWithScoreThreshold() throws QdrantException {
        // Setup
        qdrant.createCollection(TEST_COLLECTION, VECTOR_SIZE, DistanceType.COSINE);
        qdrant.insertVectors(TEST_COLLECTION, createTestVectors());
        
        Float[] queryVector = {1.0f, 0.0f, 0.0f, 0.0f};
        
        // Search without threshold
        List<SearchResult> allResults = qdrant.searchVectors(
            TEST_COLLECTION, queryVector, 10, false, false, 0.0f
        );
        
        // Search with high threshold
        List<SearchResult> filteredResults = qdrant.searchVectors(
            TEST_COLLECTION, queryVector, 10, false, false, 0.9f
        );
        
        // Filtered results should be subset of all results
        assertTrue(filteredResults.size() <= allResults.size(),
            "Filtered results should be subset of all results");
        
        // All filtered results should meet threshold
        for (SearchResult result : filteredResults) {
            assertTrue(result.getScore() >= 0.9f,
                "All results should meet score threshold");
        }
    }
    
    @Test
    @Order(8)
    @DisplayName("Test error handling")
    void testErrorHandling() {
        // Test operations on non-existent collection
        assertThrows(QdrantException.class, () -> {
            qdrant.insertVectors("non_existent", createTestVectors());
        }, "Should throw exception for non-existent collection");
        
        assertThrows(QdrantException.class, () -> {
            Float[] queryVector = {1.0f, 0.0f, 0.0f, 0.0f};
            qdrant.searchVectors("non_existent", queryVector, 5, false, false, 0.0f);
        }, "Should throw exception for search on non-existent collection");
    }
    
    @Test
    @Order(9)
    @DisplayName("Test resource cleanup")
    void testResourceCleanup() throws QdrantException {
        // Create and close multiple instances
        for (int i = 0; i < 3; i++) {
            try (QdrantEmbedded tempQdrant = new QdrantEmbedded(TEST_STORAGE_PATH + "_temp_" + i)) {
                tempQdrant.createCollection("temp_collection", VECTOR_SIZE, DistanceType.COSINE);
                // Instance should be automatically closed
            }
        }
        
        // Should not cause any issues
        assertTrue(true, "Resource cleanup should work correctly");
    }
    
    private List<VectorPoint> createTestVectors() {
        List<VectorPoint> points = new ArrayList<>();
        
        // Create test vectors with different patterns
        Float[][] vectors = {
            {1.0f, 0.0f, 0.0f, 0.0f},
            {0.0f, 1.0f, 0.0f, 0.0f},
            {0.0f, 0.0f, 1.0f, 0.0f},
            {0.0f, 0.0f, 0.0f, 1.0f},
            {0.5f, 0.5f, 0.0f, 0.0f}
        };
        
        for (int i = 0; i < vectors.length; i++) {
            Map<String, Object> payload = new HashMap<>();
            payload.put("index", i);
            payload.put("category", "test_category_" + (i % 2));
            payload.put("value", i * 10.0);
            
            points.add(new VectorPoint("test_point_" + i, vectors[i], payload));
        }
        
        return points;
    }
    
    private void cleanupTestStorage() {
        try {
            Path storagePath = Paths.get(TEST_STORAGE_PATH);
            if (Files.exists(storagePath)) {
                Files.walk(storagePath)
                    .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (Exception e) {
                            // Ignore cleanup errors
                        }
                    });
            }
            
            // Clean up temporary test storages
            for (int i = 0; i < 3; i++) {
                Path tempPath = Paths.get(TEST_STORAGE_PATH + "_temp_" + i);
                if (Files.exists(tempPath)) {
                    Files.walk(tempPath)
                        .sorted((a, b) -> b.compareTo(a))
                        .forEach(path -> {
                            try {
                                Files.delete(path);
                            } catch (Exception e) {
                                // Ignore cleanup errors
                            }
                        });
                }
            }
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }
}
