# Makefile for Qdrant JNI project

# Variables
RUST_TARGET_DIR = target/release
JAVA_DIR = java
EXAMPLES_DIR = examples/java
STORAGE_DIR = qdrant_storage

# Platform detection
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    PLATFORM = linux
    LIB_EXT = so
    LIB_PREFIX = lib
endif
ifeq ($(UNAME_S),Darwin)
    PLATFORM = macos
    LIB_EXT = dylib
    LIB_PREFIX = lib
endif
ifeq ($(OS),Windows_NT)
    PLATFORM = windows
    LIB_EXT = dll
    LIB_PREFIX = 
endif

NATIVE_LIB = $(RUST_TARGET_DIR)/$(LIB_PREFIX)qdrant_jni.$(LIB_EXT)
JAVA_JAR = $(JAVA_DIR)/target/qdrant-jni-0.1.0.jar

# Default target
.PHONY: all
all: build

# Build everything
.PHONY: build
build: rust java

# Build Rust library
.PHONY: rust
rust:
	@echo "Building Rust library for $(PLATFORM)..."
	cargo build --release
	@echo "Rust library built: $(NATIVE_LIB)"

# Build Java components
.PHONY: java
java:
	@echo "Building Java components..."
	cd $(JAVA_DIR) && mvn clean compile package -DskipTests
	@echo "Java JAR built: $(JAVA_JAR)"

# Generate JNI headers
.PHONY: headers
headers:
	@echo "Generating JNI headers..."
	cd $(JAVA_DIR) && mvn exec:exec@generate-jni-headers

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	cd $(JAVA_DIR) && mvn clean
	rm -rf $(STORAGE_DIR) qdrant_storage_*
	rm -f test_build.sh test_build.bat

# Run tests
.PHONY: test
test: build
	@echo "Running Rust tests..."
	cargo test
	@echo "Running Java tests..."
	cd $(JAVA_DIR) && mvn test

# Run basic example
.PHONY: example-basic
example-basic: build
	@echo "Running basic example..."
	cd $(EXAMPLES_DIR) && \
	javac -cp ../../$(JAVA_JAR) BasicExample.java && \
	java -cp .:../../$(JAVA_JAR) -Djava.library.path=../../$(RUST_TARGET_DIR) examples.BasicExample

# Run advanced example
.PHONY: example-advanced
example-advanced: build
	@echo "Running advanced example..."
	cd $(EXAMPLES_DIR) && \
	javac -cp ../../$(JAVA_JAR) AdvancedExample.java && \
	java -cp .:../../$(JAVA_JAR) -Djava.library.path=../../$(RUST_TARGET_DIR) examples.AdvancedExample

# Run all examples
.PHONY: examples
examples: example-basic example-advanced

# Install dependencies (requires package managers)
.PHONY: install-deps
install-deps:
	@echo "Installing dependencies..."
	@echo "Please ensure you have:"
	@echo "  - Rust (https://rustup.rs/)"
	@echo "  - Java JDK 11+ with JAVA_HOME set"
	@echo "  - Maven (optional, for Java build)"

# Check environment
.PHONY: check-env
check-env:
	@echo "Checking environment..."
	@echo "Platform: $(PLATFORM)"
	@echo "JAVA_HOME: $(JAVA_HOME)"
	@which cargo || (echo "ERROR: Rust/Cargo not found" && exit 1)
	@which javac || (echo "ERROR: Java compiler not found" && exit 1)
	@which mvn || echo "WARNING: Maven not found (Java build may fail)"
	@echo "Environment check passed!"

# Verify build
.PHONY: verify
verify: build
	@echo "Verifying build..."
	@test -f $(NATIVE_LIB) || (echo "ERROR: Native library not found: $(NATIVE_LIB)" && exit 1)
	@test -f $(JAVA_JAR) || (echo "ERROR: Java JAR not found: $(JAVA_JAR)" && exit 1)
	@echo "✓ Native library found: $(NATIVE_LIB)"
	@echo "✓ Java JAR found: $(JAVA_JAR)"
	@echo "Build verification passed!"

# Format code
.PHONY: format
format:
	@echo "Formatting Rust code..."
	cargo fmt
	@echo "Formatting Java code..."
	cd $(JAVA_DIR) && mvn spotless:apply || echo "Spotless not configured"

# Lint code
.PHONY: lint
lint:
	@echo "Linting Rust code..."
	cargo clippy -- -D warnings
	@echo "Linting Java code..."
	cd $(JAVA_DIR) && mvn checkstyle:check || echo "Checkstyle not configured"

# Development build (debug mode)
.PHONY: dev
dev:
	@echo "Building in development mode..."
	cargo build
	cd $(JAVA_DIR) && mvn compile

# Release build with optimizations
.PHONY: release
release: clean
	@echo "Building release version..."
	cargo build --release
	cd $(JAVA_DIR) && mvn clean package

# Package for distribution
.PHONY: package
package: release
	@echo "Creating distribution package..."
	mkdir -p dist
	cp $(NATIVE_LIB) dist/
	cp $(JAVA_JAR) dist/
	cp README.md dist/
	cp -r examples dist/
	@echo "Distribution package created in dist/"

# Help
.PHONY: help
help:
	@echo "Qdrant JNI Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build everything (default)"
	@echo "  build            - Build Rust library and Java components"
	@echo "  rust             - Build only Rust library"
	@echo "  java             - Build only Java components"
	@echo "  headers          - Generate JNI headers"
	@echo "  clean            - Clean all build artifacts"
	@echo "  test             - Run all tests"
	@echo "  examples         - Run all examples"
	@echo "  example-basic    - Run basic example"
	@echo "  example-advanced - Run advanced example"
	@echo "  check-env        - Check build environment"
	@echo "  verify           - Verify build output"
	@echo "  format           - Format source code"
	@echo "  lint             - Lint source code"
	@echo "  dev              - Development build"
	@echo "  release          - Release build"
	@echo "  package          - Create distribution package"
	@echo "  help             - Show this help"

# Phony targets to avoid conflicts with files
.PHONY: all build rust java headers clean test examples check-env verify format lint dev release package help
