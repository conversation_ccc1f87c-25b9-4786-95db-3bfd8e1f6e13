# Qdrant JNI - Java Bindings for Qdrant Embedded Mode

This project provides Java bindings for Qdrant's embedded mode through JNI (Java Native Interface), allowing you to use Qdrant as an in-process vector database directly from Java applications.

## Features

- **Embedded Mode**: Run Qdrant in-process without requiring a separate server
- **Full CRUD Operations**: Create, read, update, and delete vector collections and points
- **High Performance**: Direct memory access through JNI for optimal performance
- **Cross-Platform**: Supports Windows, macOS, and Linux
- **Type Safety**: Strongly-typed Java API with proper error handling
- **Resource Management**: Automatic cleanup with try-with-resources pattern

## Architecture

```
┌─────────────────┐    JNI     ┌──────────────────┐    Rust    ┌─────────────────┐
│   Java API      │ ◄────────► │   JNI Interface  │ ◄────────► │ Qdrant Embedded │
│                 │            │                  │            │                 │
│ - QdrantEmbedded│            │ - Memory Mgmt    │            │ - Vector Ops    │
│ - VectorPoint   │            │ - Error Handling │            │ - Collections   │
│ - SearchResult  │            │ - Type Conversion│            │ - Storage       │
└─────────────────┘            └──────────────────┘            └─────────────────┘
```

## Prerequisites

- **Java**: JDK 11 or higher
- **Rust**: Latest stable version (install from [rustup.rs](https://rustup.rs/))
- **Maven**: For building Java components (optional)
- **JAVA_HOME**: Environment variable pointing to your Java installation

## Quick Start

### 1. Build the Project

#### On Linux/macOS:
```bash
chmod +x build.sh
./build.sh
```

#### On Windows:
```cmd
build.bat
```

### 2. Verify the Build
```bash
# Linux/macOS
./test_build.sh

# Windows
test_build.bat
```

### 3. Run the Example
```bash
cd examples/java
javac -cp ../../java/target/qdrant-jni-0.1.0.jar BasicExample.java
java -cp .:../../java/target/qdrant-jni-0.1.0.jar -Djava.library.path=../../target/release BasicExample
```

## API Reference

### QdrantEmbedded

The main class for interacting with the embedded Qdrant instance.

```java
// Create instance
QdrantEmbedded qdrant = new QdrantEmbedded("./storage_path");

// Create collection
qdrant.createCollection("my_collection", 128, DistanceType.COSINE);

// Insert vectors
List<VectorPoint> points = Arrays.asList(
    new VectorPoint("id1", new Float[]{1.0f, 0.0f, 0.0f}, payload)
);
qdrant.insertVectors("my_collection", points);

// Search vectors
List<SearchResult> results = qdrant.searchVectors(
    "my_collection", 
    queryVector, 
    10,     // limit
    true,   // with payload
    false,  // with vector
    0.0f    // score threshold
);

// Clean up
qdrant.close();
```

### VectorPoint

Represents a vector with ID, data, and optional payload.

```java
Map<String, Object> payload = new HashMap<>();
payload.put("category", "example");
payload.put("value", 42);

VectorPoint point = new VectorPoint("unique_id", vectorData, payload);
```

### SearchResult

Contains search results with similarity scores.

```java
for (SearchResult result : results) {
    System.out.println("ID: " + result.getId());
    System.out.println("Score: " + result.getScore());
    System.out.println("Payload: " + result.getPayload());
}
```

### Distance Types

- `DistanceType.COSINE`: Cosine similarity (good for normalized vectors)
- `DistanceType.EUCLIDEAN`: Euclidean distance (L2 norm)
- `DistanceType.DOT`: Dot product similarity

## Configuration

### Collection Configuration

Collections are configured when created:

```java
qdrant.createCollection(
    "collection_name",
    vectorSize,           // Dimension of vectors
    DistanceType.COSINE   // Distance metric
);
```

### Search Parameters

Search behavior can be customized:

```java
List<SearchResult> results = qdrant.searchVectors(
    collectionName,
    queryVector,
    limit,              // Maximum results
    withPayload,        // Include payload in results
    withVector,         // Include vector data in results
    scoreThreshold      // Minimum similarity score (0.0 to disable)
);
```

## Performance Considerations

### Memory Management
- Use try-with-resources for automatic cleanup
- Close QdrantEmbedded instances when done
- Batch operations for better performance

### Vector Operations
- Normalize vectors for cosine similarity
- Use appropriate vector dimensions (typically 128-1536)
- Consider payload size impact on memory usage

### Batch Processing
```java
// Insert in batches for better performance
List<VectorPoint> batch = new ArrayList<>();
for (VectorPoint point : allPoints) {
    batch.add(point);
    if (batch.size() >= 100) {
        qdrant.insertVectors(collectionName, batch);
        batch.clear();
    }
}
```

## Error Handling

The library provides specific exception types:

```java
try {
    qdrant.createCollection("test", 128, DistanceType.COSINE);
} catch (QdrantClientException e) {
    // Qdrant-specific errors
} catch (QdrantSerializationException e) {
    // JSON serialization errors
} catch (QdrantException e) {
    // General Qdrant errors
}
```

## Building from Source

### Dependencies

The Rust project uses these key dependencies:
- `qdrant-client`: Qdrant client library
- `jni`: JNI bindings for Rust
- `serde`: Serialization framework
- `tokio`: Async runtime

### Build Process

1. **Rust Library**: Compiled as a `cdylib` for JNI compatibility
2. **Java Classes**: Compiled with Maven, includes JNI header generation
3. **Native Library**: Platform-specific shared library (`.so`, `.dll`, `.dylib`)

### Cross-Compilation

For cross-platform builds, use Rust's target system:

```bash
# For Windows from Linux
cargo build --release --target x86_64-pc-windows-gnu

# For macOS from Linux  
cargo build --release --target x86_64-apple-darwin
```

## Troubleshooting

### Common Issues

1. **UnsatisfiedLinkError**: 
   - Ensure native library is in `java.library.path`
   - Check that library architecture matches JVM architecture

2. **JAVA_HOME not set**:
   - Set JAVA_HOME environment variable
   - Ensure it points to JDK, not JRE

3. **Build failures**:
   - Update Rust: `rustup update`
   - Clean build: `cargo clean && cargo build --release`

### Debug Mode

Enable debug logging:

```bash
RUST_LOG=debug cargo build --release --features debug
```

## Examples

See the `examples/` directory for:
- `BasicExample.java`: Simple CRUD operations
- `AdvancedExample.java`: Batch operations and performance testing

## License

This project is licensed under MIT OR Apache-2.0.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review example code
- Open an issue on GitHub
