package com.qdrant;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;

/**
 * Represents a vector point with ID, vector data, and optional payload.
 */
public class VectorPoint {
    private String id;
    private Float[] vector;
    private Map<String, Object> payload;
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Default constructor.
     */
    public VectorPoint() {
    }
    
    /**
     * Constructor for JNI usage.
     * 
     * @param id Point ID
     * @param vector Vector data
     * @param payloadJson Payload as JSON string
     */
    public VectorPoint(String id, Float[] vector, String payloadJson) {
        this.id = id;
        this.vector = vector;
        this.payload = parsePayload(payloadJson);
    }
    
    /**
     * Constructor with all fields.
     * 
     * @param id Point ID
     * @param vector Vector data
     * @param payload Payload map
     */
    public VectorPoint(String id, Float[] vector, Map<String, Object> payload) {
        this.id = id;
        this.vector = vector;
        this.payload = payload;
    }
    
    /**
     * Get the point ID.
     * 
     * @return Point ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Set the point ID.
     * 
     * @param id Point ID
     */
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * Get the vector data.
     * 
     * @return Vector data
     */
    public Float[] getVector() {
        return vector;
    }
    
    /**
     * Set the vector data.
     * 
     * @param vector Vector data
     */
    public void setVector(Float[] vector) {
        this.vector = vector;
    }
    
    /**
     * Get the payload.
     * 
     * @return Payload map
     */
    public Map<String, Object> getPayload() {
        return payload;
    }
    
    /**
     * Set the payload.
     * 
     * @param payload Payload map
     */
    public void setPayload(Map<String, Object> payload) {
        this.payload = payload;
    }
    
    /**
     * Get the payload as JSON string for JNI usage.
     * 
     * @return Payload as JSON string, empty string if no payload
     */
    public String getPayloadJson() {
        if (payload == null || payload.isEmpty()) {
            return "";
        }
        
        try {
            return objectMapper.writeValueAsString(payload);
        } catch (JsonProcessingException e) {
            return "";
        }
    }
    
    /**
     * Parse payload from JSON string.
     * 
     * @param payloadJson JSON string
     * @return Payload map, null if empty or invalid
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parsePayload(String payloadJson) {
        if (payloadJson == null || payloadJson.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(payloadJson, Map.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
    
    @Override
    public String toString() {
        return String.format("VectorPoint{id='%s', vectorSize=%d, hasPayload=%b}", 
                id, vector != null ? vector.length : 0, payload != null && !payload.isEmpty());
    }
}
