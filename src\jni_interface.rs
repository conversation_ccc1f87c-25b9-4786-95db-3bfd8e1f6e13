use crate::embedded::QdrantEmbedded;
use crate::error::{QdrantError, Result};
use crate::types::*;
use jni::objects::{JClass, JObject, JString, JValue};
use jni::sys::{j<PERSON>lean, jfloat, jint, jlong, jobject, jobjectArray, jstring};
use jni::JNIEnv;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::runtime::Runtime;

// Global runtime for async operations
lazy_static::lazy_static! {
    static ref RUNTIME: Runtime = Runtime::new().expect("Failed to create Tokio runtime");
}

// Helper function to handle errors and throw Java exceptions
fn handle_error(env: &JNIEnv, error: QdrantError) -> jlong {
    let exception_class = error.to_java_exception_class();
    let message = error.to_string();
    
    if let Err(e) = env.throw_new(exception_class, message) {
        eprintln!("Failed to throw Java exception: {}", e);
    }
    
    0 // Return null pointer on error
}

// Helper function to convert Java string to Rust string
fn jstring_to_string(env: &JNIEnv, jstr: JString) -> Result<String> {
    env.get_string(jstr)
        .map(|s| s.into())
        .map_err(QdrantError::Jni)
}

// Helper function to convert Rust string to Java string
fn string_to_jstring(env: &JNIEnv, s: &str) -> Result<jstring> {
    env.new_string(s)
        .map(|s| s.into_raw())
        .map_err(QdrantError::Jni)
}

// Helper function to convert Java float array to Rust Vec<f32>
fn jfloat_array_to_vec(env: &JNIEnv, array: jobjectArray) -> Result<Vec<f32>> {
    let length = env.get_array_length(array).map_err(QdrantError::Jni)? as usize;
    let mut vec = Vec::with_capacity(length);
    
    for i in 0..length {
        let element = env.get_object_array_element(array, i as jint).map_err(QdrantError::Jni)?;
        let float_obj = JObject::from(element);
        let value = env.call_method(float_obj, "floatValue", "()F", &[]).map_err(QdrantError::Jni)?;
        
        if let JValue::Float(f) = value {
            vec.push(f);
        } else {
            return Err(QdrantError::InvalidConfiguration("Invalid float value".to_string()));
        }
    }
    
    Ok(vec)
}

// Helper function to convert Vec<f32> to Java float array
fn vec_to_jfloat_array(env: &JNIEnv, vec: &[f32]) -> Result<jobjectArray> {
    let float_class = env.find_class("java/lang/Float").map_err(QdrantError::Jni)?;
    let array = env.new_object_array(vec.len() as jint, float_class, JObject::null()).map_err(QdrantError::Jni)?;
    
    for (i, &value) in vec.iter().enumerate() {
        let float_obj = env.new_object(
            float_class,
            "(F)V",
            &[JValue::Float(value)]
        ).map_err(QdrantError::Jni)?;
        
        env.set_object_array_element(array, i as jint, float_obj).map_err(QdrantError::Jni)?;
    }
    
    Ok(array)
}

/// Create a new QdrantEmbedded instance
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_createNative(
    env: JNIEnv,
    _class: JClass,
    storage_path: JString,
) -> jlong {
    let storage_path = match jstring_to_string(&env, storage_path) {
        Ok(path) => path,
        Err(e) => return handle_error(&env, e),
    };

    let result = RUNTIME.block_on(async {
        QdrantEmbedded::new(storage_path).await
    });

    match result {
        Ok(embedded) => Box::into_raw(Box::new(embedded)) as jlong,
        Err(e) => handle_error(&env, e),
    }
}

/// Destroy QdrantEmbedded instance
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_destroyNative(
    _env: JNIEnv,
    _class: JClass,
    handle: jlong,
) {
    if handle != 0 {
        unsafe {
            let _ = Box::from_raw(handle as *mut QdrantEmbedded);
        }
    }
}

/// Create a collection
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_createCollectionNative(
    env: JNIEnv,
    _class: JClass,
    handle: jlong,
    collection_name: JString,
    vector_size: jint,
    distance_type: jint, // 0=Cosine, 1=Euclidean, 2=Dot
) -> jboolean {
    if handle == 0 {
        handle_error(&env, QdrantError::InvalidConfiguration("Invalid handle".to_string()));
        return 0;
    }

    let embedded = unsafe { &*(handle as *const QdrantEmbedded) };
    
    let collection_name = match jstring_to_string(&env, collection_name) {
        Ok(name) => name,
        Err(e) => {
            handle_error(&env, e);
            return 0;
        }
    };

    let distance = match distance_type {
        0 => Distance::Cosine,
        1 => Distance::Euclidean,
        2 => Distance::Dot,
        _ => {
            handle_error(&env, QdrantError::InvalidConfiguration("Invalid distance type".to_string()));
            return 0;
        }
    };

    let config = CollectionConfig {
        name: collection_name,
        vector_size: vector_size as usize,
        distance,
        hnsw_config: Some(HnswConfig::default()),
        optimizers_config: Some(OptimizersConfig::default()),
    };

    let result = RUNTIME.block_on(async {
        embedded.create_collection(config).await
    });

    match result {
        Ok(_) => 1,
        Err(e) => {
            handle_error(&env, e);
            0
        }
    }
}

/// Insert vectors into a collection
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_insertVectorsNative(
    env: JNIEnv,
    _class: JClass,
    handle: jlong,
    collection_name: JString,
    ids: jobjectArray,
    vectors: jobjectArray,
    payloads: jobjectArray, // JSON strings
) -> jboolean {
    if handle == 0 {
        handle_error(&env, QdrantError::InvalidConfiguration("Invalid handle".to_string()));
        return 0;
    }

    let embedded = unsafe { &*(handle as *const QdrantEmbedded) };
    
    let collection_name = match jstring_to_string(&env, collection_name) {
        Ok(name) => name,
        Err(e) => {
            handle_error(&env, e);
            return 0;
        }
    };

    // Convert Java arrays to Rust vectors
    let points = match convert_java_arrays_to_points(&env, ids, vectors, payloads) {
        Ok(points) => points,
        Err(e) => {
            handle_error(&env, e);
            return 0;
        }
    };

    let result = RUNTIME.block_on(async {
        embedded.insert_vectors(&collection_name, points).await
    });

    match result {
        Ok(_) => 1,
        Err(e) => {
            handle_error(&env, e);
            0
        }
    }
}

// Helper function to convert Java arrays to VectorPoint structs
fn convert_java_arrays_to_points(
    env: &JNIEnv,
    ids: jobjectArray,
    vectors: jobjectArray,
    payloads: jobjectArray,
) -> Result<Vec<VectorPoint>> {
    let ids_length = env.get_array_length(ids).map_err(QdrantError::Jni)? as usize;
    let vectors_length = env.get_array_length(vectors).map_err(QdrantError::Jni)? as usize;
    let payloads_length = env.get_array_length(payloads).map_err(QdrantError::Jni)? as usize;

    if ids_length != vectors_length || ids_length != payloads_length {
        return Err(QdrantError::InvalidConfiguration(
            "Array lengths must match".to_string()
        ));
    }

    let mut points = Vec::with_capacity(ids_length);

    for i in 0..ids_length {
        // Get ID
        let id_obj = env.get_object_array_element(ids, i as jint).map_err(QdrantError::Jni)?;
        let id = jstring_to_string(env, JString::from(id_obj))?;

        // Get vector
        let vector_obj = env.get_object_array_element(vectors, i as jint).map_err(QdrantError::Jni)?;
        let vector = jfloat_array_to_vec(env, vector_obj)?;

        // Get payload
        let payload_obj = env.get_object_array_element(payloads, i as jint).map_err(QdrantError::Jni)?;
        let payload_str = jstring_to_string(env, JString::from(payload_obj))?;
        let payload: Option<HashMap<String, serde_json::Value>> = if payload_str.is_empty() {
            None
        } else {
            Some(serde_json::from_str(&payload_str).map_err(QdrantError::Serialization)?)
        };

        points.push(VectorPoint {
            id,
            vector,
            payload,
        });
    }

    Ok(points)
}

/// Search for similar vectors
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_searchVectorsNative(
    env: JNIEnv,
    _class: JClass,
    handle: jlong,
    collection_name: JString,
    query_vector: jobjectArray,
    limit: jint,
    with_payload: jboolean,
    with_vector: jboolean,
    score_threshold: jfloat,
) -> jobject {
    if handle == 0 {
        handle_error(&env, QdrantError::InvalidConfiguration("Invalid handle".to_string()));
        return JObject::null().into_raw();
    }

    let embedded = unsafe { &*(handle as *const QdrantEmbedded) };

    let collection_name = match jstring_to_string(&env, collection_name) {
        Ok(name) => name,
        Err(e) => {
            handle_error(&env, e);
            return JObject::null().into_raw();
        }
    };

    let query_vector = match jfloat_array_to_vec(&env, query_vector) {
        Ok(vec) => vec,
        Err(e) => {
            handle_error(&env, e);
            return JObject::null().into_raw();
        }
    };

    let params = SearchParams {
        limit: limit as usize,
        offset: None,
        with_payload: with_payload != 0,
        with_vector: with_vector != 0,
        score_threshold: if score_threshold > 0.0 { Some(score_threshold) } else { None },
        filter: None,
    };

    let result = RUNTIME.block_on(async {
        embedded.search_vectors(&collection_name, query_vector, params).await
    });

    match result {
        Ok(search_results) => {
            match convert_search_results_to_java(&env, search_results) {
                Ok(java_array) => java_array,
                Err(e) => {
                    handle_error(&env, e);
                    JObject::null().into_raw()
                }
            }
        }
        Err(e) => {
            handle_error(&env, e);
            JObject::null().into_raw()
        }
    }
}

/// Delete vectors by IDs
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_deleteVectorsNative(
    env: JNIEnv,
    _class: JClass,
    handle: jlong,
    collection_name: JString,
    point_ids: jobjectArray,
) -> jboolean {
    if handle == 0 {
        handle_error(&env, QdrantError::InvalidConfiguration("Invalid handle".to_string()));
        return 0;
    }

    let embedded = unsafe { &*(handle as *const QdrantEmbedded) };

    let collection_name = match jstring_to_string(&env, collection_name) {
        Ok(name) => name,
        Err(e) => {
            handle_error(&env, e);
            return 0;
        }
    };

    // Convert Java string array to Rust Vec<String>
    let ids_length = match env.get_array_length(point_ids) {
        Ok(len) => len as usize,
        Err(e) => {
            handle_error(&env, QdrantError::Jni(e));
            return 0;
        }
    };

    let mut ids = Vec::with_capacity(ids_length);
    for i in 0..ids_length {
        let id_obj = match env.get_object_array_element(point_ids, i as jint) {
            Ok(obj) => obj,
            Err(e) => {
                handle_error(&env, QdrantError::Jni(e));
                return 0;
            }
        };

        let id = match jstring_to_string(&env, JString::from(id_obj)) {
            Ok(s) => s,
            Err(e) => {
                handle_error(&env, e);
                return 0;
            }
        };

        ids.push(id);
    }

    let result = RUNTIME.block_on(async {
        embedded.delete_vectors(&collection_name, ids).await
    });

    match result {
        Ok(_) => 1,
        Err(e) => {
            handle_error(&env, e);
            0
        }
    }
}

/// Get vector by ID
#[no_mangle]
pub extern "system" fn Java_com_qdrant_QdrantEmbedded_getVectorNative(
    env: JNIEnv,
    _class: JClass,
    handle: jlong,
    collection_name: JString,
    point_id: JString,
    with_payload: jboolean,
    with_vector: jboolean,
) -> jobject {
    if handle == 0 {
        handle_error(&env, QdrantError::InvalidConfiguration("Invalid handle".to_string()));
        return JObject::null().into_raw();
    }

    let embedded = unsafe { &*(handle as *const QdrantEmbedded) };

    let collection_name = match jstring_to_string(&env, collection_name) {
        Ok(name) => name,
        Err(e) => {
            handle_error(&env, e);
            return JObject::null().into_raw();
        }
    };

    let point_id = match jstring_to_string(&env, point_id) {
        Ok(id) => id,
        Err(e) => {
            handle_error(&env, e);
            return JObject::null().into_raw();
        }
    };

    let result = RUNTIME.block_on(async {
        embedded.get_vector(&collection_name, &point_id, with_payload != 0, with_vector != 0).await
    });

    match result {
        Ok(Some(vector_point)) => {
            match convert_vector_point_to_java(&env, vector_point) {
                Ok(java_obj) => java_obj,
                Err(e) => {
                    handle_error(&env, e);
                    JObject::null().into_raw()
                }
            }
        }
        Ok(None) => JObject::null().into_raw(),
        Err(e) => {
            handle_error(&env, e);
            JObject::null().into_raw()
        }
    }
}

// Helper function to convert search results to Java objects
fn convert_search_results_to_java(env: &JNIEnv, results: Vec<SearchResult>) -> Result<jobject> {
    let search_result_class = env.find_class("com/qdrant/SearchResult").map_err(QdrantError::Jni)?;
    let array = env.new_object_array(results.len() as jint, search_result_class, JObject::null()).map_err(QdrantError::Jni)?;

    for (i, result) in results.iter().enumerate() {
        let id_jstring = string_to_jstring(env, &result.id)?;
        let score = result.score;

        let payload_jstring = if let Some(ref payload) = result.payload {
            let payload_json = serde_json::to_string(payload).map_err(QdrantError::Serialization)?;
            string_to_jstring(env, &payload_json)?
        } else {
            string_to_jstring(env, "")?
        };

        let vector_array = if let Some(ref vector) = result.vector {
            vec_to_jfloat_array(env, vector)?
        } else {
            env.new_object_array(0, env.find_class("java/lang/Float").map_err(QdrantError::Jni)?, JObject::null()).map_err(QdrantError::Jni)?
        };

        let search_result_obj = env.new_object(
            search_result_class,
            "(Ljava/lang/String;F[Ljava/lang/Float;Ljava/lang/String;)V",
            &[
                JValue::Object(JObject::from_raw(id_jstring)),
                JValue::Float(score),
                JValue::Object(JObject::from_raw(vector_array)),
                JValue::Object(JObject::from_raw(payload_jstring)),
            ]
        ).map_err(QdrantError::Jni)?;

        env.set_object_array_element(array, i as jint, search_result_obj).map_err(QdrantError::Jni)?;
    }

    Ok(array)
}

// Helper function to convert VectorPoint to Java object
fn convert_vector_point_to_java(env: &JNIEnv, point: VectorPoint) -> Result<jobject> {
    let vector_point_class = env.find_class("com/qdrant/VectorPoint").map_err(QdrantError::Jni)?;

    let id_jstring = string_to_jstring(env, &point.id)?;
    let vector_array = vec_to_jfloat_array(env, &point.vector)?;

    let payload_jstring = if let Some(payload) = point.payload {
        let payload_json = serde_json::to_string(&payload).map_err(QdrantError::Serialization)?;
        string_to_jstring(env, &payload_json)?
    } else {
        string_to_jstring(env, "")?
    };

    let vector_point_obj = env.new_object(
        vector_point_class,
        "(Ljava/lang/String;[Ljava/lang/Float;Ljava/lang/String;)V",
        &[
            JValue::Object(JObject::from_raw(id_jstring)),
            JValue::Object(JObject::from_raw(vector_array)),
            JValue::Object(JObject::from_raw(payload_jstring)),
        ]
    ).map_err(QdrantError::Jni)?;

    Ok(vector_point_obj.into_raw())
}
