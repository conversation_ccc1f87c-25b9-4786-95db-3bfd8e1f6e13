use std::env;
use std::path::PathBuf;

fn main() {
    // Get the Java home directory
    let java_home = env::var("JAVA_HOME")
        .expect("JAVA_HOME environment variable must be set");
    
    let java_home_path = PathBuf::from(java_home);
    
    // Determine the platform-specific include paths
    let (include_path, platform_include_path) = if cfg!(target_os = "windows") {
        (
            java_home_path.join("include"),
            java_home_path.join("include").join("win32"),
        )
    } else if cfg!(target_os = "macos") {
        (
            java_home_path.join("include"),
            java_home_path.join("include").join("darwin"),
        )
    } else {
        // Linux and other Unix-like systems
        (
            java_home_path.join("include"),
            java_home_path.join("include").join("linux"),
        )
    };
    
    // Add the include paths to the compiler
    println!("cargo:rustc-link-search=native={}", java_home_path.join("lib").display());
    
    // Tell cargo to rerun this build script if JAVA_HOME changes
    println!("cargo:rerun-if-env-changed=JAVA_HOME");
    
    // Tell cargo to rerun this build script if any of the source files change
    println!("cargo:rerun-if-changed=src/");
    
    // Set up platform-specific linking
    if cfg!(target_os = "windows") {
        println!("cargo:rustc-link-lib=jvm");
    } else if cfg!(target_os = "macos") {
        println!("cargo:rustc-link-lib=jvm");
        println!("cargo:rustc-link-search=native={}", java_home_path.join("lib").join("server").display());
    } else {
        // Linux
        println!("cargo:rustc-link-lib=jvm");
        println!("cargo:rustc-link-search=native={}", java_home_path.join("lib").join("server").display());
    }
    
    // Generate C header file using cbindgen if available
    if let Ok(crate_dir) = env::var("CARGO_MANIFEST_DIR") {
        let config = cbindgen::Config {
            language: cbindgen::Language::C,
            include_guard: Some("QDRANT_JNI_H".to_string()),
            includes: vec!["jni.h".to_string()],
            ..Default::default()
        };
        
        if let Err(e) = cbindgen::Builder::new()
            .with_crate(crate_dir)
            .with_config(config)
            .generate()
        {
            eprintln!("Warning: Failed to generate C header: {}", e);
        }
    }
}
