use thiserror::Error;

pub type Result<T> = std::result::Result<T, QdrantError>;

#[derive(Error, Debug)]
pub enum QdrantError {
    #[error("Qdrant client error: {0}")]
    Client(#[from] qdrant_client::QdrantError),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("JNI error: {0}")]
    Jni(#[from] jni::errors::Error),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Collection not found: {0}")]
    CollectionNotFound(String),
    
    #[error("Invalid vector dimension: expected {expected}, got {actual}")]
    InvalidVectorDimension { expected: usize, actual: usize },
    
    #[error("Invalid configuration: {0}")]
    InvalidConfiguration(String),
    
    #[error("Operation failed: {0}")]
    OperationFailed(String),
    
    #[error("Async runtime error: {0}")]
    Runtime(String),
}

impl QdrantError {
    pub fn to_java_exception_class(&self) -> &'static str {
        match self {
            QdrantError::Client(_) => "com/qdrant/QdrantClientException",
            QdrantError::Serialization(_) => "com/qdrant/QdrantSerializationException",
            QdrantError::Jni(_) => "com/qdrant/QdrantJniException",
            QdrantError::Io(_) => "java/io/IOException",
            QdrantError::CollectionNotFound(_) => "com/qdrant/CollectionNotFoundException",
            QdrantError::InvalidVectorDimension { .. } => "com/qdrant/InvalidVectorDimensionException",
            QdrantError::InvalidConfiguration(_) => "com/qdrant/InvalidConfigurationException",
            QdrantError::OperationFailed(_) => "com/qdrant/OperationFailedException",
            QdrantError::Runtime(_) => "com/qdrant/RuntimeException",
        }
    }
}
