package com.qdrant;

/**
 * Base exception class for Qdrant operations.
 */
public class QdrantException extends Exception {
    
    /**
     * Constructs a new QdrantException with the specified detail message.
     * 
     * @param message the detail message
     */
    public QdrantException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new QdrantException with the specified detail message and cause.
     * 
     * @param message the detail message
     * @param cause the cause
     */
    public QdrantException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Constructs a new QdrantException with the specified cause.
     * 
     * @param cause the cause
     */
    public QdrantException(Throwable cause) {
        super(cause);
    }
}

/**
 * Exception thrown when there's a client-related error.
 */
class QdrantClientException extends QdrantException {
    public QdrantClientException(String message) {
        super(message);
    }
    
    public QdrantClientException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when there's a serialization error.
 */
class QdrantSerializationException extends QdrantException {
    public QdrantSerializationException(String message) {
        super(message);
    }
    
    public QdrantSerializationException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when there's a JNI-related error.
 */
class QdrantJniException extends QdrantException {
    public QdrantJniException(String message) {
        super(message);
    }
    
    public QdrantJniException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when a collection is not found.
 */
class CollectionNotFoundException extends QdrantException {
    public CollectionNotFoundException(String message) {
        super(message);
    }
    
    public CollectionNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when vector dimensions are invalid.
 */
class InvalidVectorDimensionException extends QdrantException {
    public InvalidVectorDimensionException(String message) {
        super(message);
    }
    
    public InvalidVectorDimensionException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when configuration is invalid.
 */
class InvalidConfigurationException extends QdrantException {
    public InvalidConfigurationException(String message) {
        super(message);
    }
    
    public InvalidConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when an operation fails.
 */
class OperationFailedException extends QdrantException {
    public OperationFailedException(String message) {
        super(message);
    }
    
    public OperationFailedException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when there's a runtime error.
 */
class RuntimeException extends QdrantException {
    public RuntimeException(String message) {
        super(message);
    }
    
    public RuntimeException(String message, Throwable cause) {
        super(message, cause);
    }
}
