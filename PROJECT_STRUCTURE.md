# Project Structure

This document describes the complete structure of the Qdrant JNI project.

## Directory Layout

```
qdrant-jni/
├── Cargo.toml                 # Rust project configuration
├── build.rs                   # Rust build script for JNI setup
├── Makefile                   # Build automation
├── build.sh                   # Cross-platform build script (Unix)
├── build.bat                  # Cross-platform build script (Windows)
├── README.md                  # Main project documentation
├── PROJECT_STRUCTURE.md       # This file
│
├── src/                       # Rust source code
│   ├── lib.rs                 # Main library entry point
│   ├── embedded.rs            # Qdrant embedded wrapper implementation
│   ├── jni_interface.rs       # JNI bindings and C-compatible functions
│   ├── error.rs               # Error types and handling
│   └── types.rs               # Common data structures
│
├── java/                      # Java components
│   ├── pom.xml                # Maven project configuration
│   ├── src/
│   │   ├── main/java/com/qdrant/
│   │   │   ├── QdrantEmbedded.java      # Main Java API class
│   │   │   ├── VectorPoint.java         # Vector point data structure
│   │   │   ├── SearchResult.java        # Search result data structure
│   │   │   ├── DistanceType.java        # Distance metric enumeration
│   │   │   └── QdrantException.java     # Exception hierarchy
│   │   └── test/java/com/qdrant/
│   │       └── QdrantEmbeddedTest.java  # Comprehensive test suite
│   └── target/                # Maven build output (generated)
│       └── qdrant-jni-0.1.0.jar
│
├── examples/                  # Usage examples
│   └── java/
│       ├── BasicExample.java     # Simple CRUD operations example
│       └── AdvancedExample.java  # Advanced usage and performance testing
│
├── target/                    # Rust build output (generated)
│   └── release/
│       ├── libqdrant_jni.so   # Linux shared library
│       ├── libqdrant_jni.dylib # macOS dynamic library
│       └── qdrant_jni.dll     # Windows DLL
│
└── include/                   # Generated JNI headers (optional)
    └── com_qdrant_QdrantEmbedded.h
```

## Component Overview

### Rust Components

#### `src/lib.rs`
- Main library entry point
- Re-exports public API
- Module organization

#### `src/embedded.rs`
- Core Qdrant embedded wrapper
- Implements `QdrantEmbedded` struct
- Provides async CRUD operations:
  - Collection management
  - Vector insertion/update/deletion
  - Vector search and retrieval
  - Batch operations

#### `src/jni_interface.rs`
- JNI-compatible function exports
- Memory management between Rust and Java
- Type conversion utilities
- Error handling and exception throwing
- Global async runtime management

#### `src/error.rs`
- Comprehensive error type definitions
- Error conversion for JNI
- Mapping to Java exception classes

#### `src/types.rs`
- Common data structures
- Serialization/deserialization support
- Configuration types for collections and search

### Java Components

#### `QdrantEmbedded.java`
- Main Java API class
- Resource management with AutoCloseable
- Native method declarations
- High-level operations wrapper

#### `VectorPoint.java`
- Represents vector data with ID and payload
- JSON serialization support
- Constructor overloads for different use cases

#### `SearchResult.java`
- Search result representation
- Includes similarity score and metadata
- Payload deserialization

#### `DistanceType.java`
- Enumeration of supported distance metrics
- JNI value mapping

#### `QdrantException.java`
- Exception hierarchy for different error types
- Specific exceptions for different failure modes

### Build System

#### `Cargo.toml`
- Rust dependencies and configuration
- JNI-specific build settings (`cdylib`)
- Feature flags and optimization settings

#### `build.rs`
- Rust build script
- JAVA_HOME detection and validation
- Platform-specific JNI linking
- Optional C header generation

#### `pom.xml`
- Maven project configuration
- Java dependencies (Jackson for JSON)
- JNI header generation
- Test configuration

#### `Makefile`
- Cross-platform build automation
- Development and release targets
- Example execution
- Environment validation

### Examples and Tests

#### `BasicExample.java`
- Simple usage demonstration
- CRUD operations walkthrough
- Error handling examples

#### `AdvancedExample.java`
- Performance testing
- Batch operations
- Multiple collections
- Complex search scenarios

#### `QdrantEmbeddedTest.java`
- Comprehensive test suite
- Unit tests for all operations
- Error condition testing
- Resource cleanup verification

## Build Artifacts

### Native Libraries
- **Linux**: `libqdrant_jni.so`
- **macOS**: `libqdrant_jni.dylib`
- **Windows**: `qdrant_jni.dll`

### Java Artifacts
- **JAR**: `qdrant-jni-0.1.0.jar`
- **Classes**: Compiled `.class` files
- **Headers**: Generated JNI headers (optional)

## Dependencies

### Rust Dependencies
- `qdrant-client`: Vector database client
- `qdrant-segment`: Embedded storage engine
- `jni`: JNI bindings for Rust
- `serde`: Serialization framework
- `tokio`: Async runtime
- `anyhow`/`thiserror`: Error handling
- `uuid`: ID generation
- `lazy_static`: Global state management

### Java Dependencies
- `jackson-core`/`jackson-databind`: JSON processing
- `junit-jupiter`: Testing framework

## Configuration

### Environment Variables
- `JAVA_HOME`: Required for JNI compilation
- `RUST_LOG`: Optional debug logging

### Runtime Configuration
- Storage path: Configurable database location
- Collection settings: Vector size, distance metric
- Search parameters: Limits, thresholds, filters

## Platform Support

### Supported Platforms
- **Linux**: x86_64, ARM64
- **macOS**: x86_64, ARM64 (Apple Silicon)
- **Windows**: x86_64

### Requirements
- **Java**: JDK 11 or higher
- **Rust**: Latest stable version
- **Maven**: Optional for Java build

## Development Workflow

1. **Setup**: Install dependencies, set JAVA_HOME
2. **Build**: Run `make build` or platform-specific scripts
3. **Test**: Run `make test` for comprehensive testing
4. **Examples**: Run `make examples` to verify functionality
5. **Package**: Run `make package` for distribution

## Integration Points

### JNI Interface
- Function naming: `Java_com_qdrant_ClassName_methodName`
- Memory management: Proper cleanup of JNI references
- Error handling: Exception throwing to Java layer
- Type conversion: Between Rust and Java types

### Async Runtime
- Global Tokio runtime for async operations
- Blocking bridge for synchronous Java API
- Thread safety considerations

### Storage
- Embedded Qdrant instance
- File-based persistence
- Configurable storage location
