#!/bin/bash

# Cross-platform build script for Qdrant JNI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if JAVA_HOME is set
if [ -z "$JAVA_HOME" ]; then
    print_error "JAVA_HOME environment variable is not set"
    print_error "Please set JAVA_HOME to your Java installation directory"
    exit 1
fi

print_status "Using JAVA_HOME: $JAVA_HOME"

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    print_error "Rust/Cargo is not installed"
    print_error "Please install Rust from https://rustup.rs/"
    exit 1
fi

# Check if Ma<PERSON> is installed (for Java build)
if ! command -v mvn &> /dev/null; then
    print_warning "Maven is not installed. Java build will be skipped."
    SKIP_JAVA=true
else
    SKIP_JAVA=false
fi

# Determine the target platform
PLATFORM=$(uname -s)
case $PLATFORM in
    Linux*)     PLATFORM_NAME="linux";;
    Darwin*)    PLATFORM_NAME="macos";;
    CYGWIN*|MINGW*|MSYS*) PLATFORM_NAME="windows";;
    *)          PLATFORM_NAME="unknown";;
esac

print_status "Building for platform: $PLATFORM_NAME"

# Create necessary directories
mkdir -p target/release
mkdir -p include

# Build the Rust library
print_status "Building Rust library..."
cargo build --release

if [ $? -ne 0 ]; then
    print_error "Rust build failed"
    exit 1
fi

print_status "Rust library built successfully"

# Copy the built library to a standard location
case $PLATFORM_NAME in
    linux)
        if [ -f "target/release/libqdrant_jni.so" ]; then
            cp target/release/libqdrant_jni.so target/release/
            print_status "Linux shared library ready: target/release/libqdrant_jni.so"
        fi
        ;;
    macos)
        if [ -f "target/release/libqdrant_jni.dylib" ]; then
            cp target/release/libqdrant_jni.dylib target/release/
            print_status "macOS dynamic library ready: target/release/libqdrant_jni.dylib"
        fi
        ;;
    windows)
        if [ -f "target/release/qdrant_jni.dll" ]; then
            cp target/release/qdrant_jni.dll target/release/
            print_status "Windows DLL ready: target/release/qdrant_jni.dll"
        fi
        ;;
esac

# Build Java components if Maven is available
if [ "$SKIP_JAVA" = false ]; then
    print_status "Building Java components..."
    cd java
    
    # Compile Java classes
    mvn clean compile
    
    if [ $? -ne 0 ]; then
        print_error "Java compilation failed"
        exit 1
    fi
    
    # Generate JNI headers
    mvn exec:exec@generate-jni-headers
    
    # Package JAR
    mvn package -DskipTests
    
    if [ $? -ne 0 ]; then
        print_error "Java packaging failed"
        exit 1
    fi
    
    cd ..
    print_status "Java components built successfully"
else
    print_warning "Skipping Java build (Maven not available)"
fi

# Create a simple test to verify the build
print_status "Creating test verification..."

cat > test_build.sh << 'EOF'
#!/bin/bash
echo "Testing Qdrant JNI build..."

# Check if the native library exists
PLATFORM=$(uname -s)
case $PLATFORM in
    Linux*)     LIB_FILE="target/release/libqdrant_jni.so";;
    Darwin*)    LIB_FILE="target/release/libqdrant_jni.dylib";;
    CYGWIN*|MINGW*|MSYS*) LIB_FILE="target/release/qdrant_jni.dll";;
    *)          echo "Unknown platform"; exit 1;;
esac

if [ -f "$LIB_FILE" ]; then
    echo "✓ Native library found: $LIB_FILE"
else
    echo "✗ Native library not found: $LIB_FILE"
    exit 1
fi

# Check if Java JAR exists
if [ -f "java/target/qdrant-jni-0.1.0.jar" ]; then
    echo "✓ Java JAR found: java/target/qdrant-jni-0.1.0.jar"
else
    echo "✗ Java JAR not found"
fi

echo "Build verification completed successfully!"
EOF

chmod +x test_build.sh

print_status "Build completed successfully!"
print_status "Run './test_build.sh' to verify the build"
print_status ""
print_status "To use the library:"
print_status "1. Add the native library to your Java library path"
print_status "2. Include the JAR file in your Java classpath"
print_status "3. See examples/ directory for usage examples"
