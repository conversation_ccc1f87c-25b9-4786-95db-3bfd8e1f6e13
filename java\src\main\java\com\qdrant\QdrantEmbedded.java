package com.qdrant;

import java.io.Closeable;
import java.util.List;
import java.util.ArrayList;

/**
 * Java wrapper for Qdrant embedded mode with JNI bindings.
 * This class provides a Java-friendly API for interacting with Qdrant's embedded vector database.
 */
public class QdrantEmbedded implements Closeable {
    
    static {
        // Load the native library
        System.loadLibrary("qdrant_jni");
    }
    
    private long nativeHandle;
    private boolean closed = false;
    
    /**
     * Create a new QdrantEmbedded instance.
     * 
     * @param storagePath Path to the storage directory for the embedded database
     * @throws QdrantException if initialization fails
     */
    public QdrantEmbedded(String storagePath) throws QdrantException {
        this.nativeHandle = createNative(storagePath);
        if (this.nativeHandle == 0) {
            throw new QdrantException("Failed to create QdrantEmbedded instance");
        }
    }
    
    /**
     * Create a new vector collection.
     * 
     * @param collectionName Name of the collection
     * @param vectorSize Dimension of the vectors
     * @param distanceType Distance metric (0=Cosine, 1=Euclidean, 2=Dot)
     * @return true if successful, false otherwise
     * @throws QdrantException if the operation fails
     */
    public boolean createCollection(String collectionName, int vectorSize, DistanceType distanceType) 
            throws QdrantException {
        checkNotClosed();
        return createCollectionNative(nativeHandle, collectionName, vectorSize, distanceType.getValue());
    }
    
    /**
     * Insert vectors into a collection.
     * 
     * @param collectionName Name of the collection
     * @param points List of vector points to insert
     * @return true if successful, false otherwise
     * @throws QdrantException if the operation fails
     */
    public boolean insertVectors(String collectionName, List<VectorPoint> points) throws QdrantException {
        checkNotClosed();
        
        String[] ids = new String[points.size()];
        Float[][] vectors = new Float[points.size()][];
        String[] payloads = new String[points.size()];
        
        for (int i = 0; i < points.size(); i++) {
            VectorPoint point = points.get(i);
            ids[i] = point.getId();
            vectors[i] = point.getVector();
            payloads[i] = point.getPayloadJson();
        }
        
        return insertVectorsNative(nativeHandle, collectionName, ids, vectors, payloads);
    }
    
    /**
     * Search for similar vectors.
     * 
     * @param collectionName Name of the collection
     * @param queryVector Query vector
     * @param limit Maximum number of results
     * @param withPayload Include payload in results
     * @param withVector Include vector in results
     * @param scoreThreshold Minimum score threshold (0.0 to disable)
     * @return List of search results
     * @throws QdrantException if the operation fails
     */
    public List<SearchResult> searchVectors(String collectionName, Float[] queryVector, int limit, 
            boolean withPayload, boolean withVector, float scoreThreshold) throws QdrantException {
        checkNotClosed();
        
        SearchResult[] results = searchVectorsNative(nativeHandle, collectionName, queryVector, 
                limit, withPayload, withVector, scoreThreshold);
        
        List<SearchResult> resultList = new ArrayList<>();
        if (results != null) {
            for (SearchResult result : results) {
                if (result != null) {
                    resultList.add(result);
                }
            }
        }
        
        return resultList;
    }
    
    /**
     * Delete vectors by IDs.
     * 
     * @param collectionName Name of the collection
     * @param pointIds List of point IDs to delete
     * @return true if successful, false otherwise
     * @throws QdrantException if the operation fails
     */
    public boolean deleteVectors(String collectionName, List<String> pointIds) throws QdrantException {
        checkNotClosed();
        String[] ids = pointIds.toArray(new String[0]);
        return deleteVectorsNative(nativeHandle, collectionName, ids);
    }
    
    /**
     * Get a specific vector by ID.
     * 
     * @param collectionName Name of the collection
     * @param pointId ID of the point to retrieve
     * @param withPayload Include payload in result
     * @param withVector Include vector in result
     * @return VectorPoint if found, null otherwise
     * @throws QdrantException if the operation fails
     */
    public VectorPoint getVector(String collectionName, String pointId, boolean withPayload, 
            boolean withVector) throws QdrantException {
        checkNotClosed();
        return getVectorNative(nativeHandle, collectionName, pointId, withPayload, withVector);
    }
    
    /**
     * Close the QdrantEmbedded instance and free native resources.
     */
    @Override
    public void close() {
        if (!closed && nativeHandle != 0) {
            destroyNative(nativeHandle);
            nativeHandle = 0;
            closed = true;
        }
    }
    
    /**
     * Ensure the instance is not closed.
     * 
     * @throws QdrantException if the instance is closed
     */
    private void checkNotClosed() throws QdrantException {
        if (closed || nativeHandle == 0) {
            throw new QdrantException("QdrantEmbedded instance is closed");
        }
    }
    
    // Native method declarations
    private static native long createNative(String storagePath);
    private static native void destroyNative(long handle);
    private static native boolean createCollectionNative(long handle, String collectionName, 
            int vectorSize, int distanceType);
    private static native boolean insertVectorsNative(long handle, String collectionName, 
            String[] ids, Float[][] vectors, String[] payloads);
    private static native SearchResult[] searchVectorsNative(long handle, String collectionName, 
            Float[] queryVector, int limit, boolean withPayload, boolean withVector, float scoreThreshold);
    private static native boolean deleteVectorsNative(long handle, String collectionName, String[] pointIds);
    private static native VectorPoint getVectorNative(long handle, String collectionName, String pointId, 
            boolean withPayload, boolean withVector);
    
    /**
     * Finalize method to ensure native resources are cleaned up.
     */
    @Override
    protected void finalize() throws Throwable {
        try {
            close();
        } finally {
            super.finalize();
        }
    }
}
