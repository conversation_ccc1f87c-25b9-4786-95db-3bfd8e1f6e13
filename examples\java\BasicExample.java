package examples;

import com.qdrant.*;
import java.util.*;

/**
 * Basic example demonstrating Qdrant JNI usage.
 */
public class BasicExample {
    
    public static void main(String[] args) {
        try {
            // Create a new QdrantEmbedded instance
            String storagePath = "./qdrant_storage";
            System.out.println("Creating QdrantEmbedded instance with storage path: " + storagePath);
            
            try (QdrantEmbedded qdrant = new QdrantEmbedded(storagePath)) {
                
                // Create a collection
                String collectionName = "example_collection";
                int vectorSize = 4;
                DistanceType distanceType = DistanceType.COSINE;
                
                System.out.println("Creating collection: " + collectionName);
                boolean success = qdrant.createCollection(collectionName, vectorSize, distanceType);
                System.out.println("Collection created: " + success);
                
                // Prepare some example vectors
                List<VectorPoint> points = new ArrayList<>();
                
                // Point 1
                Map<String, Object> payload1 = new HashMap<>();
                payload1.put("category", "A");
                payload1.put("value", 1.0);
                points.add(new VectorPoint("point1", new Float[]{1.0f, 0.0f, 0.0f, 0.0f}, payload1));
                
                // Point 2
                Map<String, Object> payload2 = new HashMap<>();
                payload2.put("category", "B");
                payload2.put("value", 2.0);
                points.add(new VectorPoint("point2", new Float[]{0.0f, 1.0f, 0.0f, 0.0f}, payload2));
                
                // Point 3
                Map<String, Object> payload3 = new HashMap<>();
                payload3.put("category", "A");
                payload3.put("value", 3.0);
                points.add(new VectorPoint("point3", new Float[]{0.0f, 0.0f, 1.0f, 0.0f}, payload3));
                
                // Insert vectors
                System.out.println("Inserting " + points.size() + " vectors...");
                success = qdrant.insertVectors(collectionName, points);
                System.out.println("Vectors inserted: " + success);
                
                // Search for similar vectors
                Float[] queryVector = {1.0f, 0.1f, 0.0f, 0.0f};
                System.out.println("Searching for similar vectors...");
                
                List<SearchResult> results = qdrant.searchVectors(
                    collectionName, 
                    queryVector, 
                    3,          // limit
                    true,       // with payload
                    false,      // with vector
                    0.0f        // score threshold
                );
                
                System.out.println("Search results:");
                for (SearchResult result : results) {
                    System.out.println("  ID: " + result.getId() + 
                                     ", Score: " + String.format("%.4f", result.getScore()) +
                                     ", Payload: " + result.getPayload());
                }
                
                // Get a specific vector
                System.out.println("\nRetrieving specific vector...");
                VectorPoint retrievedPoint = qdrant.getVector(collectionName, "point1", true, true);
                if (retrievedPoint != null) {
                    System.out.println("Retrieved point: " + retrievedPoint);
                    System.out.println("  Vector: " + Arrays.toString(retrievedPoint.getVector()));
                    System.out.println("  Payload: " + retrievedPoint.getPayload());
                } else {
                    System.out.println("Point not found");
                }
                
                // Delete a vector
                System.out.println("\nDeleting vector...");
                success = qdrant.deleteVectors(collectionName, Arrays.asList("point2"));
                System.out.println("Vector deleted: " + success);
                
                // Search again to verify deletion
                System.out.println("\nSearching again after deletion...");
                results = qdrant.searchVectors(collectionName, queryVector, 3, true, false, 0.0f);
                System.out.println("Search results after deletion:");
                for (SearchResult result : results) {
                    System.out.println("  ID: " + result.getId() + 
                                     ", Score: " + String.format("%.4f", result.getScore()));
                }
                
                System.out.println("\nExample completed successfully!");
                
            } // QdrantEmbedded will be automatically closed here
            
        } catch (QdrantException e) {
            System.err.println("Qdrant error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
