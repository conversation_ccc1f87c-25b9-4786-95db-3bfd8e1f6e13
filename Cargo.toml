[package]
name = "qdrant-jni"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Qdrant embedded mode wrapper with JNI bindings for Java integration"
license = "MIT OR Apache-2.0"

[lib]
name = "qdrant_jni"
crate-type = ["cdylib"]

[dependencies]
# Qdrant dependencies
qdrant-client = { version = "1.7", features = ["serde"] }
qdrant-segment = "0.6"

# JNI dependencies
jni = "0.21"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# Vector operations
ndarray = "0.15"

# Lazy static for global runtime
lazy_static = "1.4"

[build-dependencies]
cbindgen = "0.24"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"

[features]
default = []
debug = ["log/max_level_debug"]
