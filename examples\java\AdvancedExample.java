package examples;

import com.qdrant.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Advanced example demonstrating more complex Qdrant JNI usage.
 */
public class AdvancedExample {
    
    private static final int VECTOR_SIZE = 128;
    private static final int NUM_VECTORS = 1000;
    
    public static void main(String[] args) {
        try {
            String storagePath = "./qdrant_storage_advanced";
            System.out.println("Creating QdrantEmbedded instance for advanced example...");
            
            try (QdrantEmbedded qdrant = new QdrantEmbedded(storagePath)) {
                
                // Create collections with different distance metrics
                createCollections(qdrant);
                
                // Insert large batch of vectors
                insertLargeBatch(qdrant, "cosine_collection");
                
                // Perform various search operations
                performSearchOperations(qdrant, "cosine_collection");
                
                // Demonstrate batch operations
                demonstrateBatchOperations(qdrant, "euclidean_collection");
                
                System.out.println("\nAdvanced example completed successfully!");
                
            }
            
        } catch (Exception e) {
            System.err.println("Error in advanced example: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createCollections(QdrantEmbedded qdrant) throws QdrantException {
        System.out.println("\n=== Creating Collections ===");
        
        // Create collection with Cosine distance
        boolean success = qdrant.createCollection("cosine_collection", VECTOR_SIZE, DistanceType.COSINE);
        System.out.println("Cosine collection created: " + success);
        
        // Create collection with Euclidean distance
        success = qdrant.createCollection("euclidean_collection", VECTOR_SIZE, DistanceType.EUCLIDEAN);
        System.out.println("Euclidean collection created: " + success);
        
        // Create collection with Dot product distance
        success = qdrant.createCollection("dot_collection", VECTOR_SIZE, DistanceType.DOT);
        System.out.println("Dot product collection created: " + success);
    }
    
    private static void insertLargeBatch(QdrantEmbedded qdrant, String collectionName) throws QdrantException {
        System.out.println("\n=== Inserting Large Batch ===");
        
        List<VectorPoint> points = new ArrayList<>();
        Random random = ThreadLocalRandom.current();
        
        // Generate random vectors with metadata
        for (int i = 0; i < NUM_VECTORS; i++) {
            Float[] vector = new Float[VECTOR_SIZE];
            for (int j = 0; j < VECTOR_SIZE; j++) {
                vector[j] = (float) random.nextGaussian();
            }
            
            // Normalize vector for cosine similarity
            normalizeVector(vector);
            
            Map<String, Object> payload = new HashMap<>();
            payload.put("id", i);
            payload.put("category", "category_" + (i % 10));
            payload.put("timestamp", System.currentTimeMillis() + i);
            payload.put("value", random.nextDouble() * 100);
            payload.put("tags", Arrays.asList("tag_" + (i % 5), "tag_" + ((i + 1) % 5)));
            
            points.add(new VectorPoint("point_" + i, vector, payload));
            
            // Insert in batches of 100
            if (points.size() == 100) {
                boolean success = qdrant.insertVectors(collectionName, points);
                if (!success) {
                    throw new QdrantException("Failed to insert batch");
                }
                points.clear();
                System.out.print(".");
            }
        }
        
        // Insert remaining points
        if (!points.isEmpty()) {
            boolean success = qdrant.insertVectors(collectionName, points);
            if (!success) {
                throw new QdrantException("Failed to insert final batch");
            }
        }
        
        System.out.println("\nInserted " + NUM_VECTORS + " vectors successfully");
    }
    
    private static void performSearchOperations(QdrantEmbedded qdrant, String collectionName) throws QdrantException {
        System.out.println("\n=== Performing Search Operations ===");
        
        Random random = ThreadLocalRandom.current();
        
        // Generate a random query vector
        Float[] queryVector = new Float[VECTOR_SIZE];
        for (int i = 0; i < VECTOR_SIZE; i++) {
            queryVector[i] = (float) random.nextGaussian();
        }
        normalizeVector(queryVector);
        
        // Search with different parameters
        System.out.println("1. Basic search (top 10):");
        List<SearchResult> results = qdrant.searchVectors(collectionName, queryVector, 10, true, false, 0.0f);
        printSearchResults(results, 5);
        
        System.out.println("\n2. Search with score threshold:");
        results = qdrant.searchVectors(collectionName, queryVector, 20, true, false, 0.8f);
        printSearchResults(results, 10);
        
        System.out.println("\n3. Search with vectors included:");
        results = qdrant.searchVectors(collectionName, queryVector, 5, true, true, 0.0f);
        for (SearchResult result : results) {
            System.out.println("  ID: " + result.getId() + 
                             ", Score: " + String.format("%.4f", result.getScore()) +
                             ", Vector length: " + (result.getVector() != null ? result.getVector().length : 0));
        }
        
        // Performance test
        System.out.println("\n4. Performance test (100 searches):");
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            qdrant.searchVectors(collectionName, queryVector, 10, false, false, 0.0f);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("  100 searches completed in " + (endTime - startTime) + "ms");
        System.out.println("  Average: " + String.format("%.2f", (endTime - startTime) / 100.0) + "ms per search");
    }
    
    private static void demonstrateBatchOperations(QdrantEmbedded qdrant, String collectionName) throws QdrantException {
        System.out.println("\n=== Demonstrating Batch Operations ===");
        
        // Insert a smaller batch for testing
        List<VectorPoint> testPoints = new ArrayList<>();
        Random random = ThreadLocalRandom.current();
        
        for (int i = 0; i < 50; i++) {
            Float[] vector = new Float[VECTOR_SIZE];
            for (int j = 0; j < VECTOR_SIZE; j++) {
                vector[j] = (float) random.nextGaussian();
            }
            
            Map<String, Object> payload = new HashMap<>();
            payload.put("test_id", i);
            payload.put("batch", "test_batch");
            
            testPoints.add(new VectorPoint("test_point_" + i, vector, payload));
        }
        
        System.out.println("Inserting test batch of " + testPoints.size() + " vectors...");
        boolean success = qdrant.insertVectors(collectionName, testPoints);
        System.out.println("Test batch inserted: " + success);
        
        // Retrieve some specific vectors
        System.out.println("\nRetrieving specific vectors:");
        for (int i = 0; i < 5; i++) {
            VectorPoint point = qdrant.getVector(collectionName, "test_point_" + i, true, false);
            if (point != null) {
                System.out.println("  Retrieved: " + point.getId() + " with payload: " + point.getPayload());
            }
        }
        
        // Delete some vectors
        List<String> idsToDelete = Arrays.asList("test_point_0", "test_point_1", "test_point_2");
        System.out.println("\nDeleting vectors: " + idsToDelete);
        success = qdrant.deleteVectors(collectionName, idsToDelete);
        System.out.println("Vectors deleted: " + success);
        
        // Verify deletion
        System.out.println("\nVerifying deletion:");
        for (String id : idsToDelete) {
            VectorPoint point = qdrant.getVector(collectionName, id, true, false);
            System.out.println("  " + id + " exists: " + (point != null));
        }
    }
    
    private static void normalizeVector(Float[] vector) {
        double norm = 0.0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] = (float) (vector[i] / norm);
            }
        }
    }
    
    private static void printSearchResults(List<SearchResult> results, int maxResults) {
        int count = Math.min(results.size(), maxResults);
        for (int i = 0; i < count; i++) {
            SearchResult result = results.get(i);
            System.out.println("  " + (i + 1) + ". ID: " + result.getId() + 
                             ", Score: " + String.format("%.4f", result.getScore()) +
                             ", Category: " + (result.getPayload() != null ? result.getPayload().get("category") : "N/A"));
        }
        if (results.size() > maxResults) {
            System.out.println("  ... and " + (results.size() - maxResults) + " more results");
        }
    }
}
